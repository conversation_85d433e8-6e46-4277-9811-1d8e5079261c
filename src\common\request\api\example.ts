import { request } from '@/common/request/request'

// API基础URL，可以通过环境变量配置
const baseUrl = import.meta.env.VITE_API_BASE_URL || '/api'

/**
 * 示例API接口定义
 * 这里展示了如何使用封装好的request方法
 */

// 示例：获取用户列表
export function getUserListApi(): Promise<any> {
  return request({
    method: 'GET',
    url: `${baseUrl}/users`,
    headers: {
      'Accept': 'application/json'
    }
  })
}

// 示例：创建用户
export function createUserApi(userData: any): Promise<any> {
  return request({
    method: 'POST',
    url: `${baseUrl}/users`,
    data: userData
  })
}

// 示例：更新用户
export function updateUserApi(userId: string, userData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: `${baseUrl}/users/${userId}`,
    data: userData
  })
}

// 示例：删除用户
export function deleteUserApi(userId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: `${baseUrl}/users/${userId}`
  })
}

// 示例：文件上传
export function uploadFileApi(file: File): Promise<any> {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    method: 'POST',
    url: `${baseUrl}/upload`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 示例：带参数的GET请求
export function searchApi(params: { keyword?: string; page?: number; size?: number }): Promise<any> {
  return request({
    method: 'GET',
    url: `${baseUrl}/search`,
    params
  })
}
