<template>
  <div class="lazy-image-container" :class="{ 'is-loading': isLoading, 'has-error': hasError }">
    <!-- 占位符 -->
    <div v-if="!isLoaded && !hasError" class="lazy-image-placeholder">
      <div v-if="isLoading" class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <div v-else class="placeholder-content">
        <slot name="placeholder">
          <div class="default-placeholder">📷</div>
        </slot>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="hasError" class="lazy-image-error" @click="retry">
      <slot name="error">
        <div class="error-content">
          <div class="error-icon">❌</div>
          <div class="error-text">加载失败，点击重试</div>
        </div>
      </slot>
    </div>
    
    <!-- 实际图片 -->
    <img
      ref="imageRef"
      v-show="isLoaded"
      :alt="alt"
      :class="imageClass"
      class="lazy-image"
      @load="onLoad"
      @error="onError"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useImageLazyLoad } from '../composables/useLazyLoad'

interface Props {
  src: string
  alt?: string
  placeholder?: string
  threshold?: number
  rootMargin?: string
  imageClass?: string
  eager?: boolean // 是否立即加载
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  threshold: 0.1,
  rootMargin: '50px',
  eager: false
})

const {
  imageRef,
  isLoaded,
  isLoading,
  hasError,
  startObserving,
  loadImage
} = useImageLazyLoad({
  threshold: props.threshold,
  rootMargin: props.rootMargin
})

// 重试加载
const retry = () => {
  loadImage(props.src)
}

// 加载成功
const onLoad = () => {
  // 图片加载完成的额外处理
}

// 加载失败
const onError = () => {
  // 图片加载失败的额外处理
}

// 监听src变化
watch(() => props.src, (newSrc) => {
  if (newSrc) {
    if (props.eager) {
      loadImage(newSrc)
    } else {
      startObserving(newSrc)
    }
  }
})

onMounted(() => {
  if (props.src) {
    if (props.eager) {
      loadImage(props.src)
    } else {
      startObserving(props.src)
    }
  }
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background: var(--background-base);
  border-radius: var(--mobile-border-radius);
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-lighter);
  color: var(--text-placeholder);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-base);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.placeholder-content {
  text-align: center;
}

.default-placeholder {
  font-size: 2rem;
  opacity: 0.5;
}

.lazy-image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-base);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.lazy-image-error:hover {
  background: var(--background-lighter);
}

.error-content {
  text-align: center;
  color: var(--text-secondary);
}

.error-icon {
  font-size: 1.5rem;
  margin-bottom: var(--space-2);
}

.error-text {
  font-size: var(--font-size-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .default-placeholder {
    font-size: 1.5rem;
  }
  
  .error-icon {
    font-size: 1.2rem;
  }
  
  .error-text {
    font-size: var(--font-size-xs);
  }
}

/* 加载状态动画 */
.is-loading .lazy-image-placeholder {
  background: linear-gradient(90deg, var(--background-lighter) 25%, var(--background-base) 50%, var(--background-lighter) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
