/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ExampleCard: typeof import('./src/components/ExampleCard.vue')['default']
    InfiniteScroll: typeof import('./src/components/InfiniteScroll.vue')['default']
    LazyImage: typeof import('./src/components/LazyImage.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PullRefresh: typeof import('./src/components/PullRefresh.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanCard: typeof import('vant/es')['Card']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanDivider: typeof import('vant/es')['Divider']
    VanField: typeof import('vant/es')['Field']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPopup: typeof import('vant/es')['Popup']
    VanProgress: typeof import('vant/es')['Progress']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanTag: typeof import('vant/es')['Tag']
  }
}
