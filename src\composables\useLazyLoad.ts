/**
 * 懒加载组合式函数
 */
import { ref, onMounted, onUnmounted } from 'vue'

export interface LazyLoadOptions {
  threshold?: number // 触发加载的阈值
  rootMargin?: string // 根边距
  once?: boolean // 是否只加载一次
}

// 图片懒加载
export function useImageLazyLoad(options: LazyLoadOptions = {}) {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    once = true
  } = options

  const isLoaded = ref(false)
  const isLoading = ref(false)
  const hasError = ref(false)
  const imageRef = ref<HTMLImageElement>()
  
  let observer: IntersectionObserver | null = null
  
  const loadImage = (src: string) => {
    if (isLoaded.value || isLoading.value) return
    
    isLoading.value = true
    hasError.value = false
    
    const img = new Image()
    
    img.onload = () => {
      if (imageRef.value) {
        imageRef.value.src = src
        isLoaded.value = true
        isLoading.value = false
        
        if (once && observer) {
          observer.disconnect()
        }
      }
    }
    
    img.onerror = () => {
      hasError.value = true
      isLoading.value = false
    }
    
    img.src = src
  }
  
  const startObserving = (src: string) => {
    if (!imageRef.value || !('IntersectionObserver' in window)) {
      // 不支持 IntersectionObserver 时直接加载
      loadImage(src)
      return
    }
    
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadImage(src)
          }
        })
      },
      {
        threshold,
        rootMargin
      }
    )
    
    observer.observe(imageRef.value)
  }
  
  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }
  
  onUnmounted(() => {
    stopObserving()
  })
  
  return {
    imageRef,
    isLoaded,
    isLoading,
    hasError,
    startObserving,
    stopObserving,
    loadImage
  }
}

// 内容懒加载
export function useContentLazyLoad(callback: () => void, options: LazyLoadOptions = {}) {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    once = true
  } = options

  const isVisible = ref(false)
  const elementRef = ref<HTMLElement>()
  
  let observer: IntersectionObserver | null = null
  
  const startObserving = () => {
    if (!elementRef.value || !('IntersectionObserver' in window)) {
      // 不支持 IntersectionObserver 时直接执行
      callback()
      return
    }
    
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            isVisible.value = true
            callback()
            
            if (once && observer) {
              observer.disconnect()
            }
          } else {
            isVisible.value = false
          }
        })
      },
      {
        threshold,
        rootMargin
      }
    )
    
    observer.observe(elementRef.value)
  }
  
  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }
  
  onMounted(() => {
    startObserving()
  })
  
  onUnmounted(() => {
    stopObserving()
  })
  
  return {
    elementRef,
    isVisible,
    startObserving,
    stopObserving
  }
}

// 列表懒加载
export function useListLazyLoad<T>(
  loadMore: () => Promise<T[]>,
  options: LazyLoadOptions = {}
) {
  const {
    threshold = 0.1,
    rootMargin = '100px'
  } = options

  const items = ref<T[]>([])
  const isLoading = ref(false)
  const isFinished = ref(false)
  const hasError = ref(false)
  const sentinelRef = ref<HTMLElement>()
  
  let observer: IntersectionObserver | null = null
  
  const load = async () => {
    if (isLoading.value || isFinished.value) return
    
    isLoading.value = true
    hasError.value = false
    
    try {
      const newItems = await loadMore()
      
      if (newItems.length === 0) {
        isFinished.value = true
      } else {
        items.value.push(...newItems)
      }
    } catch (error) {
      hasError.value = true
      console.error('加载数据失败:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  const startObserving = () => {
    if (!sentinelRef.value || !('IntersectionObserver' in window)) {
      return
    }
    
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            load()
          }
        })
      },
      {
        threshold,
        rootMargin
      }
    )
    
    observer.observe(sentinelRef.value)
  }
  
  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }
  
  const reset = () => {
    items.value = []
    isLoading.value = false
    isFinished.value = false
    hasError.value = false
  }
  
  onMounted(() => {
    startObserving()
  })
  
  onUnmounted(() => {
    stopObserving()
  })
  
  return {
    items,
    isLoading,
    isFinished,
    hasError,
    sentinelRef,
    load,
    reset,
    startObserving,
    stopObserving
  }
}

// 预加载
export function usePreload() {
  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  }
  
  const preloadImages = async (srcs: string[]): Promise<void> => {
    try {
      await Promise.all(srcs.map(preloadImage))
    } catch (error) {
      console.error('预加载图片失败:', error)
    }
  }
  
  const preloadScript = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.onload = () => resolve()
      script.onerror = reject
      script.src = src
      document.head.appendChild(script)
    })
  }
  
  const preloadCSS = (href: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.onload = () => resolve()
      link.onerror = reject
      link.href = href
      document.head.appendChild(link)
    })
  }
  
  return {
    preloadImage,
    preloadImages,
    preloadScript,
    preloadCSS
  }
}
