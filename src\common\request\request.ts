/* eslint-disable @typescript-eslint/no-explicit-any */
import { setError } from '@/common/utils/error'
import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'

const $axios = axios.create({
  timeout: 15000, // 15秒超时
  headers: {
    'Content-Type': 'application/json'
  }
})

$axios.interceptors.response.use(response => {
  return response
}, error => {
  if (error.response) {
    if (error.response.status === 401) {
      // todo
    }
  }
})

/**
 * 请求的包装方法
 *
 * @params options.method string
 * @params options.url string
 */
function request<T> (options: AxiosRequestConfig): Promise<T> {
  return new Promise((resolve, reject) => {
    $axios(options).then(data => {
      if (data.status === 200) {
        if (data.data.code === '0') {
          resolve(data.data.data)
        } else if (data.data.message) {
          reject(setError({
            type: 'bizError',
            code: data.data.code,
            message: data.data.message
          }))
        } else {
          // 返回字节流
          resolve(data.data as unknown as T)
        }
      } else {
        // todo
        reject(Error('serverError'))
      }
    }).catch(err => {
      reject(err)
    })
  })
}

request.get = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  let options: AxiosRequestConfig = {}
  if (config) options = { ...config }

  return request({ url, ...options })
}
request.post = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  let options: AxiosRequestConfig = {}
  if (data) options = { data }
  if (config) options = { ...options, ...config }

  return request({ url, ...options })
}

export default $axios

export {
  request
}
