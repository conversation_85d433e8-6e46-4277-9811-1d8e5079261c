/* Vant 主题定制 */

// 主色调
:root {
  --van-primary-color: #667eea;
  --van-success-color: #4caf50;
  --van-warning-color: #ff9800;
  --van-danger-color: #f44336;
  --van-info-color: #2196f3;
  
  // 文本颜色
  --van-text-color: #323233;
  --van-text-color-2: #646566;
  --van-text-color-3: #969799;
  
  // 背景颜色
  --van-background-color: #f7f8fa;
  --van-background-color-light: #fafafa;
  
  // 边框颜色
  --van-border-color: #ebedf0;
  
  // 字体大小
  --van-font-size-xs: 10px;
  --van-font-size-sm: 12px;
  --van-font-size-md: 14px;
  --van-font-size-lg: 16px;
  --van-font-size-xl: 18px;
  
  // 圆角
  --van-border-radius-sm: 4px;
  --van-border-radius-md: 8px;
  --van-border-radius-lg: 12px;
  
  // 间距
  --van-padding-base: 4px;
  --van-padding-xs: 8px;
  --van-padding-sm: 12px;
  --van-padding-md: 16px;
  --van-padding-lg: 24px;
  --van-padding-xl: 32px;
  
  // 动画时间
  --van-animation-duration-base: 0.3s;
  --van-animation-duration-fast: 0.2s;
  
  // 阴影
  --van-shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --van-shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --van-shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

// 自定义组件样式
.van-button {
  border-radius: var(--van-border-radius-md);
  font-weight: 500;
  transition: all var(--van-animation-duration-fast) ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.van-cell {
  padding: var(--van-padding-md);
  
  &:active {
    background-color: var(--van-background-color);
  }
}

.van-field {
  .van-field__control {
    font-size: var(--van-font-size-md);
  }
}

.van-search {
  .van-search__content {
    border-radius: var(--van-border-radius-md);
    background-color: var(--van-background-color-light);
  }
}

.van-pull-refresh {
  background-color: transparent;
}

.van-list {
  background-color: white;
  border-radius: var(--van-border-radius-md);
  overflow: hidden;
  margin: 0 var(--van-padding-md);
  box-shadow: var(--van-shadow-1);
}

.van-grid-item {
  .van-grid-item__content {
    padding: var(--van-padding-lg);
    border-radius: var(--van-border-radius-md);
    background-color: white;
    box-shadow: var(--van-shadow-1);
    transition: all var(--van-animation-duration-fast) ease;
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--van-shadow-2);
    }
  }
  
  .van-grid-item__icon {
    font-size: 24px;
    color: var(--van-primary-color);
  }
  
  .van-grid-item__text {
    color: var(--van-text-color);
    font-weight: 500;
    margin-top: var(--van-padding-xs);
  }
}

.van-divider {
  color: var(--van-text-color);
  font-weight: 600;
  font-size: var(--van-font-size-lg);
  
  &::before,
  &::after {
    border-color: var(--van-primary-color);
  }
}

.van-toast {
  border-radius: var(--van-border-radius-md);
  backdrop-filter: blur(10px);
}

// 移动端优化
@media (max-width: 768px) {
  .van-button {
    min-height: 44px;
    font-size: var(--van-font-size-md);
  }
  
  .van-cell {
    min-height: 44px;
    padding: var(--van-padding-md) var(--van-padding-lg);
  }
  
  .van-field {
    .van-field__control {
      font-size: var(--van-font-size-lg);
      min-height: 44px;
    }
  }
  
  .van-grid-item {
    .van-grid-item__content {
      padding: var(--van-padding-xl);
    }
    
    .van-grid-item__icon {
      font-size: 28px;
    }
    
    .van-grid-item__text {
      font-size: var(--van-font-size-md);
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  :root {
    --van-text-color: #f0f0f0;
    --van-text-color-2: #cccccc;
    --van-text-color-3: #999999;
    --van-background-color: #1a1a1a;
    --van-background-color-light: #2a2a2a;
    --van-border-color: #333333;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  :root {
    --van-border-color: #000000;
    --van-text-color: #000000;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .van-button,
  .van-cell,
  .van-grid-item .van-grid-item__content {
    transition: none;
  }
  
  .van-button:active,
  .van-grid-item .van-grid-item__content:active {
    transform: none;
  }
}
