import { ref, onMounted } from 'vue'
import { showToast, showDialog } from 'vant'
// 数据类型定义
interface ProjectItem {
    id: number
    title: string
    description: string
    status: 'active' | 'completed' | 'pending'
    createTime: string
  }
  


function useHomeView() {
    const isRefreshing = ref(false)
    const isLoading = ref(false)
    const isFinished = ref(true) // 初始设置为true，因为已有初始数据

    const items = ref<ProjectItem[]>([
        {
        id: 1,
        title: '狮子山移动端项目',
        description: '基于 Vue 3 + Vant 的移动端应用',
        status: 'active',
        createTime: '2025-07-29'
        },
        {
        id: 2,
        title: '用户管理系统',
        description: '完整的用户权限管理解决方案',
        status: 'completed',
        createTime: '2025-07-29'
        },
        {
        id: 3,
        title: '数据可视化平台',
        description: '实时数据展示和分析工具',
        status: 'pending',
        createTime: '2025-07-29'
        },
        {
        id: 4,
        title: '新项目 4',
        description: '新创建的项目描述',
        status: 'pending',
        createTime: '2025-07-29'
        },
        {
        id: 5,
        title: '新项目 5',
        description: '新创建的项目描述',
        status: 'pending',
        createTime: '2025-07-29'
        },
        {
        id: 6,
        title: '新项目 6',
        description: '新创建的项目描述',
        status: 'pending',
        createTime: '2025-07-29'
        },
        {
        id: 7,
        title: '新项目 7',
        description: '新创建的项目描述',
        status: 'pending',
        createTime: '2025-07-29'
        }
    ])

    const addItem = () => {
        const newId = Math.max(...items.value.map((item: ProjectItem) => item.id)) + 1
        items.value.unshift({
            id: newId,
            title: `新项目 ${newId}`,
            description: '新创建的项目描述',
            status: 'pending',
            createTime: new Date().toISOString().split('T')[0]
        })
        showToast('添加成功')
    }
    
    const viewItem = (id: number) => {
        const item = items.value.find(item => item.id === id)
        if (item) {
          showDialog({
            title: item.title,
            message: `描述: ${item.description}\n状态: ${item.status}\n创建时间: ${item.createTime}`,
            confirmButtonText: '确定'
          })
        }
      }

    const editItem = (id: number) => {
    showToast(`编辑项目 ${id}`)
    }
      
    const deleteItem = (id: number) => {
    showDialog({
        title: '确认删除',
        message: '确定要删除这个项目吗？',
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消'
    }).then(() => {
        items.value = items.value.filter((item: ProjectItem) => item.id !== id)
        showToast('删除成功')
    }).catch(() => {
        // 用户取消删除
    })
    }
    const handleRefresh = async () => {
    setTimeout(() => {
        // 刷新时重置状态，允许加载更多
        isFinished.value = false
        showToast('刷新成功')
        isRefreshing.value = false
    }, 1000)
    }
      
    const handleLoadMore = () => {
    if (isLoading.value || isFinished.value) return
    
    isLoading.value = true
    setTimeout(() => {
        const newItems: ProjectItem[] = Array.from({ length: 5 }, (_, index) => ({
        id: Date.now() + index,
        title: `新项目 ${items.value.length + index + 1}`,
        description: `新创建的项目描述`,
        status: ['active', 'pending', 'completed'][Math.floor(Math.random() * 3)] as 'active' | 'pending' | 'completed',
        createTime: new Date().toISOString().split('T')[0]
        }))
    
        items.value.push(...newItems)
    
        // 增加到25个项目才结束，确保有足够内容测试滚动
        if (items.value.length >= 25) {
        isFinished.value = true
        showToast('所有项目已加载完成')
        }
    
        isLoading.value = false
    }, 800)
    }

    // 生命周期
    onMounted(() => {
    // 组件初始化完成
    console.log('HomeView mounted with', items.value.length, 'items')
    })

    return {
        isRefreshing,
        isLoading,
        isFinished,
        items,
        addItem,
        viewItem,
        editItem,
        deleteItem,
        handleRefresh,
        handleLoadMore,
        showToast
    }

}

export default  useHomeView
