<script setup lang="ts">
import useHomeView from './homeView'

const {
  isRefreshing,
  isLoading,
  isFinished,
  items,
  addItem,
  viewItem,
  editItem,
  deleteItem,
  handleRefresh,
  handleLoadMore,
  showToast
} = useHomeView()

</script>

<script lang="ts">
export default {
  /** 首页 */
  name: 'HomeView'
}
</script>

<template>
  <div class="home-container">
    <div class="responsive-wrapper">
      <!-- 顶部导航栏 -->
      <van-nav-bar
        title="狮子山移动端"
        fixed
        placeholder
        class="custom-navbar"
      >
        <template #right>
          <van-icon name="setting-o" @click="showToast('设置')" size="18" />
        </template>
      </van-nav-bar>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 下拉刷新容器 -->
      <van-pull-refresh v-model="isRefreshing" @refresh="handleRefresh">
        <!-- 欢迎横幅 -->
        <div class="welcome-banner">
          <div class="banner-content">
            <img class="banner-logo" src="@/assets/logo.png" alt="">
            <div class="banner-text">
              <h2>lion rock移动端脚手架</h2>
              <p>移动端响应式开发解决方案，技术栈包括vue3.0 + typescript + vite</p>
            </div>
          </div>
        </div>


        <!-- 项目列表 -->
        <div class="projects-section">
          <div class="section-header">
            <h3>我的项目</h3>
          </div>

          <van-list
            v-model:loading="isLoading"
            :finished="isFinished"
            finished-text="没有更多项目了"
            @load="handleLoadMore"
            class="project-list"
          >
            <!-- 响应式网格布局 -->
            <div class="projects-grid">
              <div v-for="item in items" :key="item.id" class="project-card">
                <van-swipe-cell>
                  <div class="project-content" @click="viewItem(item.id)">
                    <div class="project-header">
                      <div class="project-info">
                        <h4 class="project-title">{{ item.title }}</h4>
                        <p class="project-desc">{{ item.description }}</p>
                      </div>
                      <div class="project-status">
                        <van-tag
                          :type="item.status === 'active' ? 'primary' : item.status === 'completed' ? 'success' : 'warning'"
                        >
                          {{ item.status === 'active' ? '进行中' : item.status === 'completed' ? '已完成' : '待开始' }}
                        </van-tag>
                      </div>
                    </div>
                    <div class="project-footer">
                      <span class="project-time">{{ item.createTime }}</span>
                      <van-icon name="arrow" class="arrow-icon" />
                    </div>
                  </div>
                  <template #right>
                    <van-button
                      square
                      type="primary"
                      text="编辑"
                      @click="editItem(item.id)"
                      class="swipe-action"
                    />
                    <van-button
                      square
                      type="danger"
                      text="删除"
                      @click="deleteItem(item.id)"
                      class="swipe-action"
                    />
                  </template>
                </van-swipe-cell>
              </div>
            </div>
          </van-list>
        </div>
      </van-pull-refresh>
    </div>


    </div>
  </div>
</template>

<style scoped>
/* 现代化移动端样式 */
.home-container {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 响应式容器 */
.responsive-wrapper {
  max-width: 480px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
}

/* 手机横屏适配 */
@media (min-width: 576px) and (max-width: 767px) {
  .responsive-wrapper {
    max-width: 540px;
  }
}

/* 平板竖屏适配 (iPad 竖屏) */
@media (min-width: 768px) and (max-width: 1024px) {
  .home-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .responsive-wrapper {
    max-width: 100%;
    width: 100%;
    background: #f8f9fa;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    overflow-y: auto;
    height: 100vh;
    max-height: 100vh;
    -webkit-overflow-scrolling: touch;
  }

  .custom-navbar {
    border-radius: 16px 16px 0 0;
  }
}

/* 平板横屏适配 (iPad 横屏) */
@media (min-width: 1025px) and (max-width: 1199px) {
  .home-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .responsive-wrapper {
    max-width: 100%;
    width: 100%;
    background: #f8f9fa;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    height: 100vh;
    max-height: 100vh;
    -webkit-overflow-scrolling: touch;
  }

  .custom-navbar {
    border-radius: 20px 20px 0 0;
  }
}

/* 桌面端适配 */
@media (min-width: 1200px) {
  .home-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .responsive-wrapper {
    max-width: 100%;
    width: 100%;
    background: #f8f9fa;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
    overflow-y: auto;
    height: 100vh;
    max-height: 100vh;
    -webkit-overflow-scrolling: touch;
  }

  .custom-navbar {
    border-radius: 24px 24px 0 0;
  }
}

/* 隐藏所有丑陋的边框和调试信息 */
.home-container * {
  outline: none !important;
  border: none !important;
}

.home-container *:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 隐藏开发者工具相关的边框 */
.home-container :deep(*) {
  outline: none !important;
}

/* 特别处理可能出现的调试边框 */
.home-container :deep(.van-nav-bar),
.home-container :deep(.van-search),
.home-container :deep(.van-button),
.home-container :deep(.van-popup) {
  border: none !important;
  outline: none !important;
}

/* 导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.15);
}

.custom-navbar :deep(.van-nav-bar__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
  line-height: 1.4;
}

.custom-navbar :deep(.van-icon) {
  color: white;
}

/* 大屏幕下导航栏优化 */
@media (min-width: 768px) {
  .custom-navbar {
    position: relative !important;
    border-radius: 20px 20px 0 0;
  }

  .responsive-wrapper .custom-navbar + * {
    margin-top: 0;
  }
}

/* 主要内容区域 */
.main-content {
  padding: 0;
  background: transparent;
  min-height: calc(100vh);
}

/* 大屏幕下主要内容区域优化 */
@media (min-width: 768px) {
  .main-content {
    height: calc(100vh - 60px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .responsive-wrapper :deep(.van-nav-bar--fixed) {
    position: relative !important;
  }

  .responsive-wrapper :deep(.van-nav-bar__placeholder) {
    display: none;
  }
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, #0f3f97 0%, #4b79a2 100%);
  padding: 24px 16px;
  margin-bottom: 16px;
}

/* 大屏幕下欢迎横幅优化 */
@media (min-width: 768px) {
  .welcome-banner {
    padding: 32px 16px;
    margin-bottom: 20px;
  }
}

.banner-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.banner-text {
  margin-left: 70px;
}

.banner-logo { 
  position: absolute;
  width: 60px;
}
.banner-text h2 {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  line-height: 1.4;
  word-break: break-word;
}

.banner-text p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
  word-break: break-word;
}

.banner-actions :deep(.van-button) {
  width:7rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 14px;
  line-height: 1.4;
  min-height: 35px;
  padding: 0 10px;
}

/* 平板端横幅字体优化 */
@media (min-width: 768px) {
  .banner-text h2 {
    font-size: 24px;
  }

  .banner-text p {
    font-size: 16px;
  }

  .banner-actions :deep(.van-button) {
    font-size: 16px;
    min-height: 48px;
    padding: 0 20px;
  }
}

/* 桌面端横幅字体优化 */
@media (min-width: 1200px) {
  .banner-text h2 {
    font-size: 28px;
  }

  .banner-text p {
    font-size: 18px;
  }

  .banner-actions :deep(.van-button) {
    font-size: 18px;
    min-height: 52px;
    padding: 0 24px;
  }
}

/* 搜索区域 */
.search-section {
  padding: 0 16px 16px;
}

.search-section :deep(.van-search) {
  padding: 0;
  background: transparent;
}

.search-section :deep(.van-search__content) {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;
  height: 44px;
  display: flex;
  align-items: center;
}

.search-section :deep(.van-field) {
  height: 44px;
  display: flex;
  align-items: center;
}

.search-section :deep(.van-field__control) {
  font-size: 15px;
  line-height: 1.4;
  height: auto;
  padding: 0;
  display: flex;
  align-items: center;
}

.search-section :deep(.van-field__body) {
  height: 44px;
  display: flex;
  align-items: center;
}

.search-section :deep(.van-search__content .van-field__left-icon) {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.search-section :deep(.van-search__content input) {
  font-size: 15px;
  line-height: 1.4;
  height: auto;
  padding: 0;
  border: none;
  outline: none;
  background: transparent;
}

/* 统计卡片 */
.stats-cards {
  padding: 0 16px 20px;
}

/* 大屏幕下统计卡片优化 */
@media (min-width: 768px) {
  .stats-cards {
    padding: 0 16px 24px;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.stat-item {
  background: white;
  padding: 16px 8px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-item:active {
  transform: scale(0.98);
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #323233;
  margin-bottom: 4px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-label {
  font-size: 12px;
  color: #969799;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 平板端统计卡片优化 */
@media (min-width: 768px) {
  .stat-item {
    padding: 20px 12px;
    border-radius: 12px;
    min-height: 100px;
  }

  .stat-number {
    font-size: 24px;
    margin-bottom: 6px;
  }

  .stat-label {
    font-size: 14px;
  }
}

/* 桌面端统计卡片优化 */
@media (min-width: 1200px) {
  .stat-item {
    padding: 24px 16px;
    border-radius: 12px;
    min-height: 120px;
  }

  .stat-number {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 16px;
  }
}

/* 项目列表 */
.projects-section {
  padding: 0 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.project-list {
  background: transparent;
}

/* 项目网格布局 */
.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.project-card {
  width: 100%;
}

/* 手机横屏 - 保持单列 */
@media (min-width: 576px) and (max-width: 767px) {
  .projects-grid {
    gap: 14px;
  }
}

/* 平板竖屏 - 双列布局 */
@media (min-width: 768px) and (max-width: 1024px) {
  .projects-section {
    padding: 0 16px;
  }

  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .project-content {
    padding: 18px;
  }
}

/* 平板横屏 - 三列布局 */
@media (min-width: 1025px) and (max-width: 1199px) {
  .projects-section {
    padding: 0 20px;
  }

  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 18px;
  }

  .project-content {
    padding: 20px;
  }
}

/* 桌面端 - 三列布局 */
@media (min-width: 1200px) {
  .projects-section {
    padding: 0 24px;
  }

  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .project-content {
    padding: 22px;
  }
}

.project-content {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.04);
  outline: none !important;
  transition: all 0.3s ease;
}

.project-content:active {
  transform: translateY(-1px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08), 0 0px 2px rgba(0, 0, 0, 0.12);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.project-info {
  flex: 1;
  margin-right: 12px;
}

.project-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 4px 0;
  line-height: 1.5;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-desc {
  font-size: 14px;
  color: #646566;
  margin: 0;
  line-height: 1.5;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-status {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
}

.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.project-time {
  font-size: 12px;
  color: #969799;
  line-height: 1.4;
}

.arrow-icon {
  color: #c8c9cc;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.swipe-action {
  height: 100%;
  border-radius: 0;
  font-size: 14px;
  min-width: 60px;
}

/* 平板端项目卡片字体优化 */
@media (min-width: 768px) {
  .project-title {
    font-size: 18px;
    margin-bottom: 6px;
  }

  .project-desc {
    font-size: 15px;
  }

  .project-time {
    font-size: 13px;
  }

  .arrow-icon {
    font-size: 14px;
  }

  .swipe-action {
    font-size: 15px;
    min-width: 70px;
  }

  .section-header h3 {
    font-size: 20px;
  }

  .section-header :deep(.van-button) {
    min-height: 36px;
    font-size: 14px;
  }
}

/* 桌面端项目卡片字体优化 */
@media (min-width: 1200px) {
  .project-title {
    font-size: 20px;
    margin-bottom: 8px;
  }

  .project-desc {
    font-size: 16px;
  }

  .project-time {
    font-size: 14px;
  }

  .arrow-icon {
    font-size: 16px;
  }

  .swipe-action {
    font-size: 16px;
    min-width: 80px;
  }

  .section-header h3 {
    font-size: 22px;
  }

  .section-header :deep(.van-button) {
    min-height: 40px;
    font-size: 16px;
  }
}

/* 响应式网格布局优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .projects-grid {
    /* 在平板竖屏时优化卡片布局，充分利用屏幕宽度 */
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }
}

@media (min-width: 1025px) {
  .projects-grid {
    /* 在大屏幕时确保卡片不会过度拉伸，同时充分利用空间 */
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    max-width: none;
    gap: 20px;
  }
}



/* 确保所有元素没有调试边框 */
* {
  box-sizing: border-box;
}

*:focus {
  outline: none !important;
}

/* 移除所有可能的调试样式 */
.home-container :deep(.van-hairline--bottom):after,
.home-container :deep(.van-hairline--top):after,
.home-container :deep(.van-hairline--left):after,
.home-container :deep(.van-hairline--right):after,
.home-container :deep(.van-hairline--top-bottom):after,
.home-container :deep(.van-hairline--surround):after {
  display: none !important;
}

/* 移除所有边框样式 */
.home-container :deep(*) {
  border: none !important;
  outline: none !important;
}

/* 通用样式优化 */
:deep(.van-list) {
  background: transparent;
  border: none !important;
  box-shadow: none !important;
}

:deep(.van-pull-refresh) {
  background: transparent;
}

:deep(.van-swipe-cell) {
  border-radius: 16px;
  overflow: hidden;
}

/* 统计网格响应式优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .stats-grid {
    gap: 16px;
  }
}

@media (min-width: 1025px) {
  .stats-grid {
    gap: 20px;
  }
}

/* 平板设备特殊优化 - 确保充分利用屏幕空间 */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .responsive-wrapper {
    max-width: 100%;
    margin-top: 4px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .home-container {
    padding: 4px;
  }

  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }
}

/* 防止卡片在超宽屏幕上过度拉伸 */
@media (min-width: 1400px) {
  .responsive-wrapper {
    max-width: 100%;
  }

  .projects-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }
}

/* 触摸优化 */
.project-content,
.stat-item,
.floating-button {
  min-height: 44px;
  min-width: 44px;
}

/* 平板触控优化 */
@media (min-width: 768px) {
  .project-content {
    min-height: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .project-content:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .project-content:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.06);
  }

  .stat-item {
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  }

  .stat-item:active {
    transform: scale(0.98) translateY(-1px);
  }
  

  /* 增大触控区域 */
  .banner-actions :deep(.van-button),
  .section-header :deep(.van-button) {
    min-height: 48px;
    padding: 0 20px;
  }
}

/* 桌面端交互优化 */
@media (min-width: 1200px) {
  .project-content {
    min-height: 140px;
  }

  .project-content:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15), 0 6px 16px rgba(0, 0, 0, 0.1);
  }

  .project-content:active {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
  }

  .stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .stat-item:active {
    transform: scale(0.98) translateY(-1px);
  }

  .banner-actions :deep(.van-button),
  .section-header :deep(.van-button) {
    min-height: 52px;
    padding: 0 24px;
  }
}

/* 滚动优化 */
.home-container {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

.main-content {
  overflow-x: hidden;
}

/* 确保列表容器可以正常滚动 */
.project-list {
  min-height: 200px;
  padding-bottom: 20px;
}

/* 确保下拉刷新容器高度充足 */
:deep(.van-pull-refresh) {
  min-height: calc(100vh - 200px);
}

/* 移动端滚动优化 */
@media (max-width: 767px) {
  .responsive-wrapper {
    height: 100vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .main-content {
    height: calc(100vh - 46px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* 平板设备滚动优化 */
@media (min-width: 768px) and (max-width: 1199px) {
  .responsive-wrapper {
    height: 100vh !important;
    max-height: 100vh !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  .main-content {
    height: calc(100vh - 60px) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }
}

/* 确保在大屏幕上也能正常滚动 */
@media (min-width: 1200px) {
  .responsive-wrapper {
    max-height: none;
    height: 100vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .main-content {
    height: calc(100vh - 80px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
</style>

