/**
 * 无限滚动组合式函数
 */
import { ref, onMounted, onUnmounted } from 'vue'
import { throttle } from '../utils/mobile'

export interface InfiniteScrollOptions {
  threshold?: number // 距离底部多少像素时触发加载
  onLoad?: () => Promise<void> // 加载更多回调
  disabled?: boolean // 是否禁用
  immediate?: boolean // 是否立即检查
}

export function useInfiniteScroll(options: InfiniteScrollOptions = {}) {
  const {
    threshold = 100,
    onLoad,
    disabled = false,
    immediate = true
  } = options

  const isLoading = ref(false)
  const isFinished = ref(false)
  const hasError = ref(false)
  
  const container = ref<HTMLElement>()
  
  // 检查是否需要加载更多
  const checkLoad = () => {
    if (!container.value || disabled || isLoading.value || isFinished.value) {
      return
    }
    
    const { scrollTop, scrollHeight, clientHeight } = container.value
    const distanceToBottom = scrollHeight - scrollTop - clientHeight
    
    if (distanceToBottom <= threshold) {
      loadMore()
    }
  }
  
  // 节流的检查函数
  const throttledCheck = throttle(checkLoad, 100)
  
  // 加载更多数据
  const loadMore = async () => {
    if (!onLoad || isLoading.value || isFinished.value) return
    
    isLoading.value = true
    hasError.value = false
    
    try {
      await onLoad()
    } catch (error) {
      hasError.value = true
      console.error('加载更多数据失败:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  // 重置状态
  const reset = () => {
    isLoading.value = false
    isFinished.value = false
    hasError.value = false
  }
  
  // 标记为完成
  const finish = () => {
    isFinished.value = true
    isLoading.value = false
  }
  
  // 处理滚动事件
  const handleScroll = () => {
    throttledCheck()
  }
  
  // 绑定事件
  const bindEvents = () => {
    if (!container.value) return
    
    container.value.addEventListener('scroll', handleScroll, { passive: true })
    
    // 立即检查一次
    if (immediate) {
      setTimeout(checkLoad, 100)
    }
  }
  
  // 解绑事件
  const unbindEvents = () => {
    if (!container.value) return
    
    container.value.removeEventListener('scroll', handleScroll)
  }
  
  onMounted(() => {
    bindEvents()
  })
  
  onUnmounted(() => {
    unbindEvents()
  })
  
  return {
    container,
    isLoading,
    isFinished,
    hasError,
    loadMore,
    reset,
    finish,
    bindEvents,
    unbindEvents
  }
}

// 无限滚动状态文本
export function useInfiniteScrollText() {
  const getStatusText = (isLoading: boolean, isFinished: boolean, hasError: boolean) => {
    if (hasError) return '加载失败，点击重试'
    if (isLoading) return '正在加载...'
    if (isFinished) return '没有更多数据了'
    return '上拉加载更多'
  }
  
  const getStatusIcon = (isLoading: boolean, isFinished: boolean, hasError: boolean) => {
    if (hasError) return '❌'
    if (isLoading) return '🔄'
    if (isFinished) return '✅'
    return '⬆️'
  }
  
  return {
    getStatusText,
    getStatusIcon
  }
}

// 虚拟滚动（用于大量数据优化）
export function useVirtualScroll(itemHeight: number, visibleCount: number) {
  const scrollTop = ref(0)
  const containerHeight = ref(0)
  
  const startIndex = ref(0)
  const endIndex = ref(visibleCount)
  
  const container = ref<HTMLElement>()
  
  // 计算可见范围
  const updateVisibleRange = (totalCount: number) => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(start + visibleCount + 1, totalCount)
    
    startIndex.value = Math.max(0, start)
    endIndex.value = end
  }
  
  // 处理滚动
  const handleScroll = () => {
    if (!container.value) return
    
    scrollTop.value = container.value.scrollTop
  }
  
  // 获取容器样式
  const getContainerStyle = (totalCount: number) => {
    return {
      height: `${totalCount * itemHeight}px`,
      position: 'relative' as const
    }
  }
  
  // 获取项目样式
  const getItemStyle = (index: number) => {
    return {
      position: 'absolute' as const,
      top: `${index * itemHeight}px`,
      height: `${itemHeight}px`,
      width: '100%'
    }
  }
  
  // 滚动到指定位置
  const scrollToIndex = (index: number) => {
    if (!container.value) return
    
    const targetScrollTop = index * itemHeight
    container.value.scrollTop = targetScrollTop
  }
  
  onMounted(() => {
    if (container.value) {
      container.value.addEventListener('scroll', handleScroll, { passive: true })
      containerHeight.value = container.value.clientHeight
    }
  })
  
  onUnmounted(() => {
    if (container.value) {
      container.value.removeEventListener('scroll', handleScroll)
    }
  })
  
  return {
    container,
    startIndex,
    endIndex,
    scrollTop,
    containerHeight,
    updateVisibleRange,
    getContainerStyle,
    getItemStyle,
    scrollToIndex
  }
}
