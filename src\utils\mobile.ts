/**
 * 移动端适配工具函数
 */

// 设备类型检测
export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

export const isIOS = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

export const isAndroid = (): boolean => {
  return /Android/.test(navigator.userAgent)
}

export const isWechat = (): boolean => {
  return /MicroMessenger/i.test(navigator.userAgent)
}

// 屏幕尺寸相关
export const getScreenInfo = () => {
  return {
    width: window.screen.width,
    height: window.screen.height,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

// 视口尺寸
export const getViewportSize = () => {
  return {
    width: window.innerWidth || document.documentElement.clientWidth,
    height: window.innerHeight || document.documentElement.clientHeight
  }
}

// 禁止页面滚动
export const disableScroll = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.height = '100%'
}

// 恢复页面滚动
export const enableScroll = () => {
  document.body.style.overflow = ''
  document.body.style.height = ''
}

// 防止iOS橡皮筋效果
export const preventBounce = () => {
  document.addEventListener('touchmove', (e) => {
    if (e.touches.length > 1) {
      e.preventDefault()
    }
  }, { passive: false })
}

// 防止双击缩放
export const preventZoom = () => {
  let lastTouchEnd = 0
  document.addEventListener('touchend', (e) => {
    const now = Date.now()
    if (now - lastTouchEnd <= 300) {
      e.preventDefault()
    }
    lastTouchEnd = now
  }, false)
}

// 设置安全区域CSS变量
export const setSafeAreaVars = () => {
  if (isIOS()) {
    const safeAreaTop = getComputedStyle(document.documentElement).getPropertyValue('--sat') || '0px'
    const safeAreaBottom = getComputedStyle(document.documentElement).getPropertyValue('--sab') || '0px'
    
    document.documentElement.style.setProperty('--safe-area-top', safeAreaTop)
    document.documentElement.style.setProperty('--safe-area-bottom', safeAreaBottom)
  } else {
    document.documentElement.style.setProperty('--safe-area-top', '0px')
    document.documentElement.style.setProperty('--safe-area-bottom', '0px')
  }
}

// 初始化移动端适配
export const initMobileAdaptation = () => {
  // 设置安全区域变量
  setSafeAreaVars()
  
  // 防止iOS橡皮筋效果
  if (isIOS()) {
    preventBounce()
  }
  
  // 防止双击缩放
  preventZoom()
  
  // 监听屏幕旋转
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      setSafeAreaVars()
    }, 100)
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setSafeAreaVars()
  })
}

// 触摸反馈
export const addTouchFeedback = (element: HTMLElement, className = 'touch-active') => {
  element.addEventListener('touchstart', () => {
    element.classList.add(className)
  })
  
  element.addEventListener('touchend', () => {
    setTimeout(() => {
      element.classList.remove(className)
    }, 150)
  })
  
  element.addEventListener('touchcancel', () => {
    element.classList.remove(className)
  })
}

// 节流函数
export const throttle = (func: Function, delay: number) => {
  let timer: NodeJS.Timeout | null = null
  return function (this: any, ...args: any[]) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

// 防抖函数
export const debounce = (func: Function, delay: number) => {
  let timer: NodeJS.Timeout | null = null
  return function (this: any, ...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}
